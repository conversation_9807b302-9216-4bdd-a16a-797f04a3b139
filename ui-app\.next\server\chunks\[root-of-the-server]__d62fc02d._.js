module.exports = {

"[project]/.next-internal/server/app/api/process-pnr/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/process-pnr/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
// Import the main function from the parent directory
// We'll need to create a wrapper that can be imported
async function processMainFunction(pnr) {
    // For now, we'll simulate the main.js functionality
    // In a real implementation, you would:
    // 1. Import the main function from ../../../main.js
    // 2. Or create a shared module that both can use
    // 3. Or modify main.js to export its functions
    // Simulate processing delay
    await new Promise((resolve)=>setTimeout(resolve, 2000));
    // Generate mock data based on the PNR
    const mockPolicyId = `${pnr}-POLICY-${Math.random().toString(36).substring(2, 11).toUpperCase()}`;
    const recordCount = Math.floor(Math.random() * 6) + 2; // 2-7 records
    const missingCount = Math.floor(Math.random() * recordCount);
    const passengers = [
        'JOHN DOE',
        'JANE SMITH',
        'MICHAEL JOHNSON',
        'SARAH WILSON',
        'DAVID BROWN',
        'LISA DAVIS',
        'ROBERT MILLER',
        'JENNIFER GARCIA'
    ];
    const flights = [
        'GYD to DOH (FZ710/009)',
        'DOH to GYD (FZ004/707)',
        'DXB to DOH (FZ123/456)',
        'DOH to DXB (FZ789/012)'
    ];
    const statuses = [
        'ACTIVE',
        'BOARDED',
        'CANCELED',
        'NO_SHOW'
    ];
    const insuranceRecords = [];
    const sqlQueries = [];
    for(let i = 1; i <= recordCount; i++){
        const hasConfirmation = i > missingCount;
        const passenger = passengers[Math.floor(Math.random() * passengers.length)];
        const flight = flights[Math.floor(Math.random() * flights.length)];
        const status = statuses[Math.floor(Math.random() * statuses.length)];
        const record = {
            recordNumber: i,
            passenger,
            flight,
            departureDate: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16).replace('T', ' '),
            insuranceId: `${mockPolicyId}/${Math.random().toString(36).substring(2, 11)}`,
            provider: Math.random() > 0.5 ? 'Cover Genius' : 'AIG',
            status,
            channel: 'MOBILE',
            purchaseDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16).replace('T', ' '),
            hasConfirmation,
            confirmation: hasConfirmation ? Math.floor(Math.random() * **********).toString() : undefined,
            withinPolicyPeriod: Math.random() > 0.1
        };
        insuranceRecords.push(record);
        if (!hasConfirmation) {
            sqlQueries.push({
                recordNumber: i,
                passenger,
                query: `UPDATE P_FZ.RESERVATION_SEGS SET INSURANCE_CONF_NUM='${mockPolicyId}' WHERE CONFIRMATION_NUM='${pnr}' AND RECORD_NUM=${i};`
            });
        }
    }
    return {
        pnrNumber: pnr,
        policyId: mockPolicyId,
        insuranceRecords,
        policyStartDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10),
        policyEndDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10),
        summary: {
            totalRecords: recordCount,
            withConfirmation: recordCount - missingCount,
            missingConfirmation: missingCount,
            withinPolicyPeriod: insuranceRecords.filter((r)=>r.withinPolicyPeriod).length
        },
        sqlQueries
    };
}
async function POST(request) {
    try {
        const { pnr } = await request.json();
        if (!pnr || typeof pnr !== 'string') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'PNR is required and must be a string'
            }, {
                status: 400
            });
        }
        // Validate PNR format
        const cleanPnr = pnr.trim().toUpperCase();
        if (cleanPnr.length < 3 || cleanPnr.length > 10) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'PNR must be between 3 and 10 characters'
            }, {
                status: 400
            });
        }
        try {
            const result = await processMainFunction(cleanPnr);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result);
        } catch (processingError) {
            console.error('Error processing PNR:', processingError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Failed to process PNR',
                details: processingError instanceof Error ? processingError.message : 'Unknown processing error'
            }, {
                status: 500
            });
        }
    } catch (error) {
        console.error('API Error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__d62fc02d._.js.map