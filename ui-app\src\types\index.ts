export interface PNRRecord {
  pnr: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
  result?: ProcessingResult;
}

export interface ProcessingResult {
  pnrNumber: string;
  policyId?: string;
  insuranceRecords: InsuranceRecord[];
  policyStartDate?: string;
  policyEndDate?: string;
  summary: {
    totalRecords: number;
    withConfirmation: number;
    missingConfirmation: number;
    withinPolicyPeriod: number;
  };
  sqlQueries: SqlQuery[];
}

export interface InsuranceRecord {
  recordNumber: number;
  passenger: string;
  flight: string;
  departureDate: string;
  insuranceId: string;
  provider: string;
  status: string;
  channel: string;
  purchaseDate: string;
  hasConfirmation: boolean;
  confirmation?: string;
  withinPolicyPeriod: boolean;
}

export interface SqlQuery {
  recordNumber: number;
  passenger: string;
  query: string;
}

export interface BatchProcessingState {
  pnrs: PNRRecord[];
  isProcessing: boolean;
  completedCount: number;
  totalCount: number;
}
