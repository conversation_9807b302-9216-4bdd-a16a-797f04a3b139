// Test script to verify main.js integration
const path = require('path');

async function testMainFunction() {
    try {
        console.log('Testing main.js integration...');
        
        // Import the main function
        const mainPath = path.resolve(__dirname, 'main.js');
        console.log('Main path:', mainPath);
        
        const { main } = require(mainPath);
        console.log('Main function imported successfully');
        
        // Test with a sample PNR
        const testPnr = 'ALG5OR';
        console.log(`Testing with PNR: ${testPnr}`);
        
        const result = await main(testPnr, true);
        console.log('Result:', JSON.stringify(result, null, 2));
        
    } catch (error) {
        console.error('Error testing main function:', error);
    }
}

testMainFunction();
