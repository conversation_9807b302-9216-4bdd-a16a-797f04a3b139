console.log('Starting server...');

const express = require('express');
const cors = require('cors');
const path = require('path');

console.log('Express loaded');

const app = express();
const PORT = 3001;

console.log('App created');

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Import the main function
const { main } = require('./main.js');

// API endpoint to process PNR
app.post('/api/process-pnr', async (req, res) => {
  try {
    const { pnr } = req.body;

    if (!pnr || typeof pnr !== 'string') {
      return res.status(400).json({
        error: 'PNR is required and must be a string'
      });
    }

    // Validate PNR format
    const cleanPnr = pnr.trim().toUpperCase();
    if (cleanPnr.length < 3 || cleanPnr.length > 10) {
      return res.status(400).json({
        error: 'PNR must be between 3 and 10 characters'
      });
    }

    try {
      console.log(`Processing PNR: ${cleanPnr}`);
      const result = await main(cleanPnr, true);
      console.log(`Successfully processed PNR: ${cleanPnr}`);
      res.json(result);
    } catch (processingError) {
      console.error('Error processing PNR:', processingError);
      res.status(500).json({
        error: 'Failed to process PNR',
        details: processingError.message
      });
    }

  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
  console.log(`API endpoint: http://localhost:${PORT}/api/process-pnr`);
  console.log(`Health check: http://localhost:${PORT}/api/health`);
});
