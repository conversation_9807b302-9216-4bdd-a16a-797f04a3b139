{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Code/cover-genius-v1/ui-app/src/app/api/process-pnr/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\n// Import the main function from the parent directory\n// We'll need to create a wrapper that can be imported\nasync function processMainFunction(pnr: string) {\n  // For now, we'll simulate the main.js functionality\n  // In a real implementation, you would:\n  // 1. Import the main function from ../../../main.js\n  // 2. Or create a shared module that both can use\n  // 3. Or modify main.js to export its functions\n\n  // Simulate processing delay\n  await new Promise(resolve => setTimeout(resolve, 2000));\n\n  // Generate mock data based on the PNR\n  const mockPolicyId = `${pnr}-POLICY-${Math.random().toString(36).substring(2, 11).toUpperCase()}`;\n  const recordCount = Math.floor(Math.random() * 6) + 2; // 2-7 records\n  const missingCount = Math.floor(Math.random() * recordCount);\n\n  const passengers = [\n    'JOHN DOE', 'JAN<PERSON> SMITH', 'MICHAEL JOHNSON', 'SARAH WILSON',\n    'DAVID BROWN', 'LISA DAVIS', 'ROBERT MILLER', 'JENNIFER GARCIA'\n  ];\n\n  const flights = [\n    'GYD to DOH (FZ710/009)', 'DOH to GYD (FZ004/707)',\n    'DXB to DOH (FZ123/456)', 'DOH to DXB (FZ789/012)'\n  ];\n\n  const statuses = ['ACTIVE', 'BOARDED', 'CANCELED', 'NO_SHOW'];\n\n  const insuranceRecords = [];\n  const sqlQueries = [];\n\n  for (let i = 1; i <= recordCount; i++) {\n    const hasConfirmation = i > missingCount;\n    const passenger = passengers[Math.floor(Math.random() * passengers.length)];\n    const flight = flights[Math.floor(Math.random() * flights.length)];\n    const status = statuses[Math.floor(Math.random() * statuses.length)];\n\n    const record = {\n      recordNumber: i,\n      passenger,\n      flight,\n      departureDate: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16).replace('T', ' '),\n      insuranceId: `${mockPolicyId}/${Math.random().toString(36).substring(2, 11)}`,\n      provider: Math.random() > 0.5 ? 'Cover Genius' : 'AIG',\n      status,\n      channel: 'MOBILE',\n      purchaseDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16).replace('T', ' '),\n      hasConfirmation,\n      confirmation: hasConfirmation ? Math.floor(Math.random() * **********).toString() : undefined,\n      withinPolicyPeriod: Math.random() > 0.1, // 90% within policy period\n    };\n\n    insuranceRecords.push(record);\n\n    if (!hasConfirmation) {\n      sqlQueries.push({\n        recordNumber: i,\n        passenger,\n        query: `UPDATE P_FZ.RESERVATION_SEGS SET INSURANCE_CONF_NUM='${mockPolicyId}' WHERE CONFIRMATION_NUM='${pnr}' AND RECORD_NUM=${i};`\n      });\n    }\n  }\n\n  return {\n    pnrNumber: pnr,\n    policyId: mockPolicyId,\n    insuranceRecords,\n    policyStartDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10),\n    policyEndDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10),\n    summary: {\n      totalRecords: recordCount,\n      withConfirmation: recordCount - missingCount,\n      missingConfirmation: missingCount,\n      withinPolicyPeriod: insuranceRecords.filter(r => r.withinPolicyPeriod).length,\n    },\n    sqlQueries\n  };\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { pnr } = await request.json();\n\n    if (!pnr || typeof pnr !== 'string') {\n      return NextResponse.json(\n        { error: 'PNR is required and must be a string' },\n        { status: 400 }\n      );\n    }\n\n    // Validate PNR format\n    const cleanPnr = pnr.trim().toUpperCase();\n    if (cleanPnr.length < 3 || cleanPnr.length > 10) {\n      return NextResponse.json(\n        { error: 'PNR must be between 3 and 10 characters' },\n        { status: 400 }\n      );\n    }\n\n    try {\n      const result = await processMainFunction(cleanPnr);\n      return NextResponse.json(result);\n    } catch (processingError) {\n      console.error('Error processing PNR:', processingError);\n\n      return NextResponse.json(\n        {\n          error: 'Failed to process PNR',\n          details: processingError instanceof Error ? processingError.message : 'Unknown processing error'\n        },\n        { status: 500 }\n      );\n    }\n\n  } catch (error) {\n    console.error('API Error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,qDAAqD;AACrD,sDAAsD;AACtD,eAAe,oBAAoB,GAAW;IAC5C,oDAAoD;IACpD,uCAAuC;IACvC,oDAAoD;IACpD,iDAAiD;IACjD,+CAA+C;IAE/C,4BAA4B;IAC5B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;IAEjD,sCAAsC;IACtC,MAAM,eAAe,GAAG,IAAI,QAAQ,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW,IAAI;IACjG,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,GAAG,cAAc;IACrE,MAAM,eAAe,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;IAEhD,MAAM,aAAa;QACjB;QAAY;QAAc;QAAmB;QAC7C;QAAe;QAAc;QAAiB;KAC/C;IAED,MAAM,UAAU;QACd;QAA0B;QAC1B;QAA0B;KAC3B;IAED,MAAM,WAAW;QAAC;QAAU;QAAW;QAAY;KAAU;IAE7D,MAAM,mBAAmB,EAAE;IAC3B,MAAM,aAAa,EAAE;IAErB,IAAK,IAAI,IAAI,GAAG,KAAK,aAAa,IAAK;QACrC,MAAM,kBAAkB,IAAI;QAC5B,MAAM,YAAY,UAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE;QAC3E,MAAM,SAAS,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;QAClE,MAAM,SAAS,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;QAEpE,MAAM,SAAS;YACb,cAAc;YACd;YACA;YACA,eAAe,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,KAAK;YACvH,aAAa,GAAG,aAAa,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK;YAC7E,UAAU,KAAK,MAAM,KAAK,MAAM,iBAAiB;YACjD;YACA,SAAS;YACT,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,KAAK;YACtH;YACA,cAAc,kBAAkB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,QAAQ,KAAK;YACpF,oBAAoB,KAAK,MAAM,KAAK;QACtC;QAEA,iBAAiB,IAAI,CAAC;QAEtB,IAAI,CAAC,iBAAiB;YACpB,WAAW,IAAI,CAAC;gBACd,cAAc;gBACd;gBACA,OAAO,CAAC,qDAAqD,EAAE,aAAa,0BAA0B,EAAE,IAAI,iBAAiB,EAAE,EAAE,CAAC,CAAC;YACrI;QACF;IACF;IAEA,OAAO;QACL,WAAW;QACX,UAAU;QACV;QACA,iBAAiB,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,GAAG;QACxF,eAAe,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,GAAG;QACtF,SAAS;YACP,cAAc;YACd,kBAAkB,cAAc;YAChC,qBAAqB;YACrB,oBAAoB,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,kBAAkB,EAAE,MAAM;QAC/E;QACA;IACF;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,QAAQ,IAAI;QAElC,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuC,GAChD;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,WAAW,IAAI,IAAI,GAAG,WAAW;QACvC,IAAI,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,GAAG,IAAI;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0C,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,oBAAoB;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B,EAAE,OAAO,iBAAiB;YACxB,QAAQ,KAAK,CAAC,yBAAyB;YAEvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,SAAS,2BAA2B,QAAQ,gBAAgB,OAAO,GAAG;YACxE,GACA;gBAAE,QAAQ;YAAI;QAElB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}