{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Code/cover-genius-v1/ui-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Code/cover-genius-v1/ui-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Code/cover-genius-v1/ui-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Code/cover-genius-v1/ui-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Code/cover-genius-v1/ui-app/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Code/cover-genius-v1/ui-app/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Code/cover-genius-v1/ui-app/src/components/csv-upload.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback } from 'react';\nimport <PERSON> from 'papapar<PERSON>';\nimport { <PERSON><PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Badge } from '@/components/ui/badge';\nimport { Upload, FileText, X } from 'lucide-react';\n\ninterface CSVUploadProps {\n  onPNRsExtracted: (pnrs: string[]) => void;\n}\n\ninterface CSVData {\n  [key: string]: string;\n}\n\nexport function CSVUpload({ onPNRsExtracted }: CSVUploadProps) {\n  const [file, setFile] = useState<File | null>(null);\n  const [csvData, setCsvData] = useState<CSVData[]>([]);\n  const [headers, setHeaders] = useState<string[]>([]);\n  const [selectedColumn, setSelectedColumn] = useState<string>('');\n  const [error, setError] = useState<string>('');\n  const [pnrs, setPnrs] = useState<string[]>([]);\n\n  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {\n    const uploadedFile = event.target.files?.[0];\n    if (!uploadedFile) return;\n\n    if (!uploadedFile.name.toLowerCase().endsWith('.csv')) {\n      setError('Please upload a CSV file');\n      return;\n    }\n\n    setFile(uploadedFile);\n    setError('');\n\n    Papa.parse(uploadedFile, {\n      header: true,\n      skipEmptyLines: true,\n      complete: (results) => {\n        if (results.errors.length > 0) {\n          setError(`CSV parsing error: ${results.errors[0].message}`);\n          return;\n        }\n\n        const data = results.data as CSVData[];\n        setCsvData(data);\n        \n        if (data.length > 0) {\n          const csvHeaders = Object.keys(data[0]);\n          setHeaders(csvHeaders);\n          \n          // Auto-select PNR column if found\n          const pnrColumn = csvHeaders.find(header => \n            header.toLowerCase().includes('pnr') || \n            header.toLowerCase().includes('confirmation') ||\n            header.toLowerCase().includes('booking')\n          );\n          if (pnrColumn) {\n            setSelectedColumn(pnrColumn);\n          }\n        }\n      },\n      error: (error) => {\n        setError(`Failed to parse CSV: ${error.message}`);\n      }\n    });\n  }, []);\n\n  const extractPNRs = useCallback(() => {\n    if (!selectedColumn || csvData.length === 0) {\n      setError('Please select a column containing PNR numbers');\n      return;\n    }\n\n    const extractedPnrs = csvData\n      .map(row => row[selectedColumn]?.trim().toUpperCase())\n      .filter(pnr => pnr && pnr.length >= 3 && pnr.length <= 10)\n      .filter((pnr, index, array) => array.indexOf(pnr) === index); // Remove duplicates\n\n    if (extractedPnrs.length === 0) {\n      setError('No valid PNR numbers found in the selected column');\n      return;\n    }\n\n    setPnrs(extractedPnrs);\n    onPNRsExtracted(extractedPnrs);\n    setError('');\n  }, [selectedColumn, csvData, onPNRsExtracted]);\n\n  const clearFile = useCallback(() => {\n    setFile(null);\n    setCsvData([]);\n    setHeaders([]);\n    setSelectedColumn('');\n    setPnrs([]);\n    setError('');\n  }, []);\n\n  return (\n    <Card className=\"w-full\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Upload className=\"h-5 w-5\" />\n          Upload CSV File\n        </CardTitle>\n        <CardDescription>\n          Upload a CSV file containing PNR numbers to process insurance data\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {!file ? (\n          <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\">\n            <FileText className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n            <div className=\"space-y-2\">\n              <p className=\"text-sm text-gray-600\">\n                Choose a CSV file or drag and drop it here\n              </p>\n              <Input\n                type=\"file\"\n                accept=\".csv\"\n                onChange={handleFileUpload}\n                className=\"max-w-xs mx-auto\"\n              />\n            </div>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n              <div className=\"flex items-center gap-2\">\n                <FileText className=\"h-4 w-4\" />\n                <span className=\"text-sm font-medium\">{file.name}</span>\n                <Badge variant=\"secondary\">{csvData.length} rows</Badge>\n              </div>\n              <Button variant=\"ghost\" size=\"sm\" onClick={clearFile}>\n                <X className=\"h-4 w-4\" />\n              </Button>\n            </div>\n\n            {headers.length > 0 && (\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">\n                  Select column containing PNR numbers:\n                </label>\n                <select\n                  value={selectedColumn}\n                  onChange={(e) => setSelectedColumn(e.target.value)}\n                  className=\"w-full p-2 border border-gray-300 rounded-md\"\n                >\n                  <option value=\"\">-- Select Column --</option>\n                  {headers.map((header) => (\n                    <option key={header} value={header}>\n                      {header}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            )}\n\n            {selectedColumn && (\n              <div className=\"space-y-2\">\n                <p className=\"text-sm text-gray-600\">\n                  Preview of {selectedColumn} column (first 5 rows):\n                </p>\n                <div className=\"bg-gray-50 p-3 rounded-lg\">\n                  {csvData.slice(0, 5).map((row, index) => (\n                    <div key={index} className=\"text-sm\">\n                      {row[selectedColumn]}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            <Button \n              onClick={extractPNRs} \n              disabled={!selectedColumn}\n              className=\"w-full\"\n            >\n              Extract PNR Numbers ({selectedColumn ? csvData.length : 0} rows)\n            </Button>\n          </div>\n        )}\n\n        {error && (\n          <Alert variant=\"destructive\">\n            <AlertDescription>{error}</AlertDescription>\n          </Alert>\n        )}\n\n        {pnrs.length > 0 && (\n          <Alert>\n            <AlertDescription>\n              Successfully extracted {pnrs.length} unique PNR numbers: {pnrs.slice(0, 5).join(', ')}\n              {pnrs.length > 5 && ` and ${pnrs.length - 5} more...`}\n            </AlertDescription>\n          </Alert>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AATA;;;;;;;;;;AAmBO,SAAS,UAAU,EAAE,eAAe,EAAkB;IAC3D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE7C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,eAAe,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAC5C,IAAI,CAAC,cAAc;QAEnB,IAAI,CAAC,aAAa,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS;YACrD,SAAS;YACT;QACF;QAEA,QAAQ;QACR,SAAS;QAET,sIAAA,CAAA,UAAI,CAAC,KAAK,CAAC,cAAc;YACvB,QAAQ;YACR,gBAAgB;YAChB,UAAU,CAAC;gBACT,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,GAAG;oBAC7B,SAAS,CAAC,mBAAmB,EAAE,QAAQ,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE;oBAC1D;gBACF;gBAEA,MAAM,OAAO,QAAQ,IAAI;gBACzB,WAAW;gBAEX,IAAI,KAAK,MAAM,GAAG,GAAG;oBACnB,MAAM,aAAa,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;oBACtC,WAAW;oBAEX,kCAAkC;oBAClC,MAAM,YAAY,WAAW,IAAI,CAAC,CAAA,SAChC,OAAO,WAAW,GAAG,QAAQ,CAAC,UAC9B,OAAO,WAAW,GAAG,QAAQ,CAAC,mBAC9B,OAAO,WAAW,GAAG,QAAQ,CAAC;oBAEhC,IAAI,WAAW;wBACb,kBAAkB;oBACpB;gBACF;YACF;YACA,OAAO,CAAC;gBACN,SAAS,CAAC,qBAAqB,EAAE,MAAM,OAAO,EAAE;YAClD;QACF;IACF,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,CAAC,kBAAkB,QAAQ,MAAM,KAAK,GAAG;YAC3C,SAAS;YACT;QACF;QAEA,MAAM,gBAAgB,QACnB,GAAG,CAAC,CAAA,MAAO,GAAG,CAAC,eAAe,EAAE,OAAO,eACvC,MAAM,CAAC,CAAA,MAAO,OAAO,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,IAAI,IACtD,MAAM,CAAC,CAAC,KAAK,OAAO,QAAU,MAAM,OAAO,CAAC,SAAS,QAAQ,oBAAoB;QAEpF,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,SAAS;YACT;QACF;QAEA,QAAQ;QACR,gBAAgB;QAChB,SAAS;IACX,GAAG;QAAC;QAAgB;QAAS;KAAgB;IAE7C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,QAAQ;QACR,WAAW,EAAE;QACb,WAAW,EAAE;QACb,kBAAkB;QAClB,QAAQ,EAAE;QACV,SAAS;IACX,GAAG,EAAE;IAEL,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGhC,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;oBACpB,CAAC,qBACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDAGrC,8OAAC,iIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,QAAO;wCACP,UAAU;wCACV,WAAU;;;;;;;;;;;;;;;;;6CAKhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAK,WAAU;0DAAuB,KAAK,IAAI;;;;;;0DAChD,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;;oDAAa,QAAQ,MAAM;oDAAC;;;;;;;;;;;;;kDAE7C,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,SAAS;kDACzC,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAIhB,QAAQ,MAAM,GAAG,mBAChB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAsB;;;;;;kDAGvC,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;oDAAoB,OAAO;8DACzB;mDADU;;;;;;;;;;;;;;;;;4BAQpB,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;4CAAwB;4CACvB;4CAAe;;;;;;;kDAE7B,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC7B,8OAAC;gDAAgB,WAAU;0DACxB,GAAG,CAAC,eAAe;+CADZ;;;;;;;;;;;;;;;;0CAQlB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,CAAC;gCACX,WAAU;;oCACX;oCACuB,iBAAiB,QAAQ,MAAM,GAAG;oCAAE;;;;;;;;;;;;;oBAK/D,uBACC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;kCACb,cAAA,8OAAC,iIAAA,CAAA,mBAAgB;sCAAE;;;;;;;;;;;oBAItB,KAAK,MAAM,GAAG,mBACb,8OAAC,iIAAA,CAAA,QAAK;kCACJ,cAAA,8OAAC,iIAAA,CAAA,mBAAgB;;gCAAC;gCACQ,KAAK,MAAM;gCAAC;gCAAsB,KAAK,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;gCAC/E,KAAK,MAAM,GAAG,KAAK,CAAC,KAAK,EAAE,KAAK,MAAM,GAAG,EAAE,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;AAOnE", "debugId": null}}, {"offset": {"line": 716, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Code/cover-genius-v1/ui-app/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 755, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Code/cover-genius-v1/ui-app/src/components/pnr-processor.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Progress } from '@/components/ui/progress';\nimport { Badge } from '@/components/ui/badge';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Play, RotateCcw } from 'lucide-react';\nimport { PNRRecord, BatchProcessingState } from '@/types';\n\ninterface PNRProcessorProps {\n  pnrs: string[];\n  onProcessingComplete: (results: PNRRecord[]) => void;\n}\n\nexport function PNRProcessor({ pnrs, onProcessingComplete }: PNRProcessorProps) {\n  const [state, setState] = useState<BatchProcessingState>({\n    pnrs: pnrs.map(pnr => ({ pnr, status: 'pending', progress: 0 })),\n    isProcessing: false,\n    completedCount: 0,\n    totalCount: pnrs.length\n  });\n\n  const processPNR = useCallback(async (pnr: string): Promise<void> => {\n    try {\n      // Update status to processing\n      setState(prev => ({\n        ...prev,\n        pnrs: prev.pnrs.map(p =>\n          p.pnr === pnr ? { ...p, status: 'processing', progress: 10 } : p\n        )\n      }));\n\n      // Simulate progress updates\n      for (let progress = 20; progress <= 90; progress += 20) {\n        await new Promise(resolve => setTimeout(resolve, 500));\n        setState(prev => ({\n          ...prev,\n          pnrs: prev.pnrs.map(p =>\n            p.pnr === pnr ? { ...p, progress } : p\n          )\n        }));\n      }\n\n      // Call the API\n      const response = await fetch('/api/process-pnr', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ pnr }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n\n      // Update with completed status\n      setState(prev => ({\n        ...prev,\n        pnrs: prev.pnrs.map(p =>\n          p.pnr === pnr\n            ? { ...p, status: 'completed', progress: 100, result }\n            : p\n        ),\n        completedCount: prev.completedCount + 1\n      }));\n\n    } catch (error) {\n      // Update with error status\n      setState(prev => ({\n        ...prev,\n        pnrs: prev.pnrs.map(p =>\n          p.pnr === pnr\n            ? {\n                ...p,\n                status: 'error',\n                progress: 0,\n                error: error instanceof Error ? error.message : 'Unknown error'\n              }\n            : p\n        ),\n        completedCount: prev.completedCount + 1\n      }));\n    }\n  }, []);\n\n  const startProcessing = useCallback(async () => {\n    setState(prev => ({ ...prev, isProcessing: true, completedCount: 0 }));\n\n    // Process PNRs sequentially to avoid overwhelming the APIs\n    for (const pnrRecord of state.pnrs) {\n      if (pnrRecord.status === 'pending') {\n        await processPNR(pnrRecord.pnr);\n      }\n    }\n\n    setState(prev => ({ ...prev, isProcessing: false }));\n\n    // Notify parent component\n    onProcessingComplete(state.pnrs);\n  }, [state.pnrs, processPNR, onProcessingComplete]);\n\n  const resetProcessing = useCallback(() => {\n    setState({\n      pnrs: pnrs.map(pnr => ({ pnr, status: 'pending', progress: 0 })),\n      isProcessing: false,\n      completedCount: 0,\n      totalCount: pnrs.length\n    });\n  }, [pnrs]);\n\n  const getStatusBadgeVariant = (status: PNRRecord['status']) => {\n    switch (status) {\n      case 'pending': return 'secondary';\n      case 'processing': return 'default';\n      case 'completed': return 'default';\n      case 'error': return 'destructive';\n      default: return 'secondary';\n    }\n  };\n\n\n\n  const overallProgress = state.totalCount > 0\n    ? (state.completedCount / state.totalCount) * 100\n    : 0;\n\n  return (\n    <Card className=\"w-full\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center justify-between\">\n          <span>Process PNR Records</span>\n          <div className=\"flex gap-2\">\n            <Button\n              onClick={startProcessing}\n              disabled={state.isProcessing}\n              size=\"sm\"\n            >\n              <Play className=\"h-4 w-4 mr-2\" />\n              {state.isProcessing ? 'Processing...' : 'Start Processing'}\n            </Button>\n            <Button\n              onClick={resetProcessing}\n              disabled={state.isProcessing}\n              variant=\"outline\"\n              size=\"sm\"\n            >\n              <RotateCcw className=\"h-4 w-4 mr-2\" />\n              Reset\n            </Button>\n          </div>\n        </CardTitle>\n        <CardDescription>\n          Process {state.totalCount} PNR records to extract insurance data\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Overall Progress */}\n        <div className=\"space-y-2\">\n          <div className=\"flex justify-between text-sm\">\n            <span>Overall Progress</span>\n            <span>{state.completedCount} / {state.totalCount}</span>\n          </div>\n          <Progress value={overallProgress} className=\"w-full\" />\n        </div>\n\n        {/* Individual PNR Status */}\n        <div className=\"space-y-2 max-h-96 overflow-y-auto\">\n          {state.pnrs.map((pnrRecord) => (\n            <div\n              key={pnrRecord.pnr}\n              className=\"flex items-center justify-between p-3 border rounded-lg\"\n            >\n              <div className=\"flex items-center gap-3\">\n                <span className=\"font-mono font-medium\">{pnrRecord.pnr}</span>\n                <Badge variant={getStatusBadgeVariant(pnrRecord.status)}>\n                  {pnrRecord.status}\n                </Badge>\n              </div>\n\n              <div className=\"flex items-center gap-3\">\n                {pnrRecord.status === 'processing' && (\n                  <div className=\"w-24\">\n                    <Progress value={pnrRecord.progress} className=\"h-2\" />\n                  </div>\n                )}\n\n                {pnrRecord.status === 'completed' && pnrRecord.result && (\n                  <span className=\"text-sm text-green-600\">\n                    {pnrRecord.result.summary.missingConfirmation} missing\n                  </span>\n                )}\n\n                {pnrRecord.status === 'error' && (\n                  <span className=\"text-sm text-red-600 max-w-xs truncate\">\n                    {pnrRecord.error}\n                  </span>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Summary */}\n        {state.completedCount > 0 && (\n          <Alert>\n            <AlertDescription>\n              Completed: {state.pnrs.filter(p => p.status === 'completed').length},\n              Errors: {state.pnrs.filter(p => p.status === 'error').length},\n              Remaining: {state.pnrs.filter(p => p.status === 'pending').length}\n            </AlertDescription>\n          </Alert>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AARA;;;;;;;;;AAgBO,SAAS,aAAa,EAAE,IAAI,EAAE,oBAAoB,EAAqB;IAC5E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;QACvD,MAAM,KAAK,GAAG,CAAC,CAAA,MAAO,CAAC;gBAAE;gBAAK,QAAQ;gBAAW,UAAU;YAAE,CAAC;QAC9D,cAAc;QACd,gBAAgB;QAChB,YAAY,KAAK,MAAM;IACzB;IAEA,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACpC,IAAI;YACF,8BAA8B;YAC9B,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,CAAA,IAClB,EAAE,GAAG,KAAK,MAAM;4BAAE,GAAG,CAAC;4BAAE,QAAQ;4BAAc,UAAU;wBAAG,IAAI;gBAEnE,CAAC;YAED,4BAA4B;YAC5B,IAAK,IAAI,WAAW,IAAI,YAAY,IAAI,YAAY,GAAI;gBACtD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,CAAA,IAClB,EAAE,GAAG,KAAK,MAAM;gCAAE,GAAG,CAAC;gCAAE;4BAAS,IAAI;oBAEzC,CAAC;YACH;YAEA,eAAe;YACf,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAI;YAC7B;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,+BAA+B;YAC/B,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,CAAA,IAClB,EAAE,GAAG,KAAK,MACN;4BAAE,GAAG,CAAC;4BAAE,QAAQ;4BAAa,UAAU;4BAAK;wBAAO,IACnD;oBAEN,gBAAgB,KAAK,cAAc,GAAG;gBACxC,CAAC;QAEH,EAAE,OAAO,OAAO;YACd,2BAA2B;YAC3B,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,CAAA,IAClB,EAAE,GAAG,KAAK,MACN;4BACE,GAAG,CAAC;4BACJ,QAAQ;4BACR,UAAU;4BACV,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,IACA;oBAEN,gBAAgB,KAAK,cAAc,GAAG;gBACxC,CAAC;QACH;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,cAAc;gBAAM,gBAAgB;YAAE,CAAC;QAEpE,2DAA2D;QAC3D,KAAK,MAAM,aAAa,MAAM,IAAI,CAAE;YAClC,IAAI,UAAU,MAAM,KAAK,WAAW;gBAClC,MAAM,WAAW,UAAU,GAAG;YAChC;QACF;QAEA,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,cAAc;YAAM,CAAC;QAElD,0BAA0B;QAC1B,qBAAqB,MAAM,IAAI;IACjC,GAAG;QAAC,MAAM,IAAI;QAAE;QAAY;KAAqB;IAEjD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,SAAS;YACP,MAAM,KAAK,GAAG,CAAC,CAAA,MAAO,CAAC;oBAAE;oBAAK,QAAQ;oBAAW,UAAU;gBAAE,CAAC;YAC9D,cAAc;YACd,gBAAgB;YAChB,YAAY,KAAK,MAAM;QACzB;IACF,GAAG;QAAC;KAAK;IAET,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAIA,MAAM,kBAAkB,MAAM,UAAU,GAAG,IACvC,AAAC,MAAM,cAAc,GAAG,MAAM,UAAU,GAAI,MAC5C;IAEJ,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,MAAM,YAAY;wCAC5B,MAAK;;0DAEL,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,MAAM,YAAY,GAAG,kBAAkB;;;;;;;kDAE1C,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,MAAM,YAAY;wCAC5B,SAAQ;wCACR,MAAK;;0DAEL,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;kCAK5C,8OAAC,gIAAA,CAAA,kBAAe;;4BAAC;4BACN,MAAM,UAAU;4BAAC;;;;;;;;;;;;;0BAG9B,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;;4CAAM,MAAM,cAAc;4CAAC;4CAAI,MAAM,UAAU;;;;;;;;;;;;;0CAElD,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,OAAO;gCAAiB,WAAU;;;;;;;;;;;;kCAI9C,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,0BACf,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAyB,UAAU,GAAG;;;;;;0DACtD,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAS,sBAAsB,UAAU,MAAM;0DACnD,UAAU,MAAM;;;;;;;;;;;;kDAIrB,8OAAC;wCAAI,WAAU;;4CACZ,UAAU,MAAM,KAAK,8BACpB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,OAAO,UAAU,QAAQ;oDAAE,WAAU;;;;;;;;;;;4CAIlD,UAAU,MAAM,KAAK,eAAe,UAAU,MAAM,kBACnD,8OAAC;gDAAK,WAAU;;oDACb,UAAU,MAAM,CAAC,OAAO,CAAC,mBAAmB;oDAAC;;;;;;;4CAIjD,UAAU,MAAM,KAAK,yBACpB,8OAAC;gDAAK,WAAU;0DACb,UAAU,KAAK;;;;;;;;;;;;;+BAzBjB,UAAU,GAAG;;;;;;;;;;oBAkCvB,MAAM,cAAc,GAAG,mBACtB,8OAAC,iIAAA,CAAA,QAAK;kCACJ,cAAA,8OAAC,iIAAA,CAAA,mBAAgB;;gCAAC;gCACJ,MAAM,IAAI,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;gCAAC;gCAC3D,MAAM,IAAI,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,SAAS,MAAM;gCAAC;gCACjD,MAAM,IAAI,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAO/E", "debugId": null}}, {"offset": {"line": 1156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Code/cover-genius-v1/ui-app/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1274, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Code/cover-genius-v1/ui-app/src/components/results-table.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Copy, Download, Eye, EyeOff } from 'lucide-react';\nimport { PNRRecord, InsuranceRecord } from '@/types';\n\ninterface ResultsTableProps {\n  results: PNRRecord[];\n}\n\nexport function ResultsTable({ results }: ResultsTableProps) {\n\n  const [showAllRecords, setShowAllRecords] = useState<{ [key: string]: boolean }>({});\n\n  const completedResults = results.filter(r => r.status === 'completed' && r.result);\n\n  const copyToClipboard = async (text: string) => {\n    try {\n      await navigator.clipboard.writeText(text);\n      // You could add a toast notification here\n    } catch (err) {\n      console.error('Failed to copy text: ', err);\n    }\n  };\n\n  const exportResults = () => {\n    const allInsuranceRecords: Record<string, unknown>[] = [];\n    const allSqlQueries: string[] = [];\n\n    completedResults.forEach(result => {\n      if (result.result) {\n        // Add insurance records\n        result.result.insuranceRecords.forEach(record => {\n          allInsuranceRecords.push({\n            PNR: result.pnr,\n            PolicyID: result.result!.policyId,\n            ...record\n          });\n        });\n\n        // Add SQL queries\n        result.result.sqlQueries.forEach(query => {\n          allSqlQueries.push(`-- PNR: ${result.pnr}, Record: ${query.recordNumber}, Passenger: ${query.passenger}`);\n          allSqlQueries.push(query.query);\n          allSqlQueries.push('');\n        });\n      }\n    });\n\n    // Create CSV content\n    const csvContent = [\n      Object.keys(allInsuranceRecords[0] || {}).join(','),\n      ...allInsuranceRecords.map(record =>\n        Object.values(record).map(value =>\n          typeof value === 'string' && value.includes(',') ? `\"${value}\"` : value\n        ).join(',')\n      )\n    ].join('\\n');\n\n    // Create SQL content\n    const sqlContent = allSqlQueries.join('\\n');\n\n    // Download CSV\n    const csvBlob = new Blob([csvContent], { type: 'text/csv' });\n    const csvUrl = URL.createObjectURL(csvBlob);\n    const csvLink = document.createElement('a');\n    csvLink.href = csvUrl;\n    csvLink.download = `insurance-records-${new Date().toISOString().split('T')[0]}.csv`;\n    csvLink.click();\n\n    // Download SQL\n    const sqlBlob = new Blob([sqlContent], { type: 'text/sql' });\n    const sqlUrl = URL.createObjectURL(sqlBlob);\n    const sqlLink = document.createElement('a');\n    sqlLink.href = sqlUrl;\n    sqlLink.download = `update-queries-${new Date().toISOString().split('T')[0]}.sql`;\n    sqlLink.click();\n  };\n\n  const toggleShowAllRecords = (pnr: string) => {\n    setShowAllRecords(prev => ({\n      ...prev,\n      [pnr]: !prev[pnr]\n    }));\n  };\n\n  const getStatusBadge = (record: InsuranceRecord) => {\n    if (!record.hasConfirmation) {\n      return <Badge variant=\"destructive\">Missing Confirmation</Badge>;\n    }\n    if (!record.withinPolicyPeriod) {\n      return <Badge variant=\"secondary\">Outside Policy Period</Badge>;\n    }\n    return <Badge variant=\"default\">OK</Badge>;\n  };\n\n  if (completedResults.length === 0) {\n    return (\n      <Card className=\"w-full\">\n        <CardHeader>\n          <CardTitle>Processing Results</CardTitle>\n          <CardDescription>Results will appear here as PNRs are processed</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <Alert>\n            <AlertDescription>\n              No completed results yet. Start processing PNRs to see the insurance data.\n            </AlertDescription>\n          </Alert>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Summary Card */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center justify-between\">\n            <span>Processing Results Summary</span>\n            <Button onClick={exportResults} size=\"sm\">\n              <Download className=\"h-4 w-4 mr-2\" />\n              Export All\n            </Button>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">{completedResults.length}</div>\n              <div className=\"text-sm text-gray-600\">Processed PNRs</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-red-600\">\n                {completedResults.reduce((sum, r) => sum + (r.result?.summary.missingConfirmation || 0), 0)}\n              </div>\n              <div className=\"text-sm text-gray-600\">Missing Confirmations</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">\n                {completedResults.reduce((sum, r) => sum + (r.result?.summary.totalRecords || 0), 0)}\n              </div>\n              <div className=\"text-sm text-gray-600\">Total Records</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">\n                {completedResults.reduce((sum, r) => sum + (r.result?.sqlQueries.length || 0), 0)}\n              </div>\n              <div className=\"text-sm text-gray-600\">SQL Queries</div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Individual PNR Results */}\n      {completedResults.map((result) => {\n        if (!result.result) return null;\n\n        const missingRecords = result.result.insuranceRecords.filter(r => !r.hasConfirmation);\n        const showAll = showAllRecords[result.pnr];\n        const recordsToShow = showAll ? result.result.insuranceRecords : missingRecords;\n\n        return (\n          <Card key={result.pnr}>\n            <CardHeader>\n              <CardTitle className=\"flex items-center justify-between\">\n                <span>PNR: {result.pnr}</span>\n                <div className=\"flex gap-2\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => toggleShowAllRecords(result.pnr)}\n                  >\n                    {showAll ? <EyeOff className=\"h-4 w-4 mr-2\" /> : <Eye className=\"h-4 w-4 mr-2\" />}\n                    {showAll ? 'Show Missing Only' : 'Show All Records'}\n                  </Button>\n                </div>\n              </CardTitle>\n              <CardDescription>\n                Policy: {result.result.policyId} |\n                Total: {result.result.summary.totalRecords} |\n                Missing: {result.result.summary.missingConfirmation}\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              {/* Insurance Records Table */}\n              <div className=\"overflow-x-auto\">\n                <Table>\n                  <TableHeader>\n                    <TableRow>\n                      <TableHead>Record #</TableHead>\n                      <TableHead>Passenger</TableHead>\n                      <TableHead>Flight</TableHead>\n                      <TableHead>Departure</TableHead>\n                      <TableHead>Status</TableHead>\n                      <TableHead>Confirmation</TableHead>\n                    </TableRow>\n                  </TableHeader>\n                  <TableBody>\n                    {recordsToShow.map((record) => (\n                      <TableRow key={`${result.pnr}-${record.recordNumber}`}>\n                        <TableCell>{record.recordNumber}</TableCell>\n                        <TableCell>{record.passenger}</TableCell>\n                        <TableCell>{record.flight}</TableCell>\n                        <TableCell>{record.departureDate}</TableCell>\n                        <TableCell>{getStatusBadge(record)}</TableCell>\n                        <TableCell>\n                          {record.hasConfirmation ? record.confirmation : 'Missing'}\n                        </TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </div>\n\n              {/* SQL Queries */}\n              {result.result.sqlQueries.length > 0 && (\n                <div className=\"mt-6 space-y-3\">\n                  <h4 className=\"font-semibold\">SQL Update Queries</h4>\n                  {result.result.sqlQueries.map((query, index) => (\n                    <div key={index} className=\"bg-gray-900 text-gray-100 p-3 rounded-lg\">\n                      <div className=\"flex justify-between items-start mb-2\">\n                        <span className=\"text-sm text-gray-400\">\n                          Record {query.recordNumber} - {query.passenger}\n                        </span>\n                        <Button\n                          variant=\"ghost\"\n                          size=\"sm\"\n                          onClick={() => copyToClipboard(query.query)}\n                        >\n                          <Copy className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                      <code className=\"text-sm font-mono\">{query.query}</code>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        );\n      })}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAeO,SAAS,aAAa,EAAE,OAAO,EAAqB;IAEzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IAElF,MAAM,mBAAmB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,EAAE,MAAM;IAEjF,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,0CAA0C;QAC5C,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,sBAAiD,EAAE;QACzD,MAAM,gBAA0B,EAAE;QAElC,iBAAiB,OAAO,CAAC,CAAA;YACvB,IAAI,OAAO,MAAM,EAAE;gBACjB,wBAAwB;gBACxB,OAAO,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAA;oBACrC,oBAAoB,IAAI,CAAC;wBACvB,KAAK,OAAO,GAAG;wBACf,UAAU,OAAO,MAAM,CAAE,QAAQ;wBACjC,GAAG,MAAM;oBACX;gBACF;gBAEA,kBAAkB;gBAClB,OAAO,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;oBAC/B,cAAc,IAAI,CAAC,CAAC,QAAQ,EAAE,OAAO,GAAG,CAAC,UAAU,EAAE,MAAM,YAAY,CAAC,aAAa,EAAE,MAAM,SAAS,EAAE;oBACxG,cAAc,IAAI,CAAC,MAAM,KAAK;oBAC9B,cAAc,IAAI,CAAC;gBACrB;YACF;QACF;QAEA,qBAAqB;QACrB,MAAM,aAAa;YACjB,OAAO,IAAI,CAAC,mBAAmB,CAAC,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;eAC5C,oBAAoB,GAAG,CAAC,CAAA,SACzB,OAAO,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAA,QACxB,OAAO,UAAU,YAAY,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,OAClE,IAAI,CAAC;SAEV,CAAC,IAAI,CAAC;QAEP,qBAAqB;QACrB,MAAM,aAAa,cAAc,IAAI,CAAC;QAEtC,eAAe;QACf,MAAM,UAAU,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAW;QAC1D,MAAM,SAAS,IAAI,eAAe,CAAC;QACnC,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,QAAQ,IAAI,GAAG;QACf,QAAQ,QAAQ,GAAG,CAAC,kBAAkB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QACpF,QAAQ,KAAK;QAEb,eAAe;QACf,MAAM,UAAU,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAW;QAC1D,MAAM,SAAS,IAAI,eAAe,CAAC;QACnC,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,QAAQ,IAAI,GAAG;QACf,QAAQ,QAAQ,GAAG,CAAC,eAAe,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QACjF,QAAQ,KAAK;IACf;IAEA,MAAM,uBAAuB,CAAC;QAC5B,kBAAkB,CAAA,OAAQ,CAAC;gBACzB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI;YACnB,CAAC;IACH;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,OAAO,eAAe,EAAE;YAC3B,qBAAO,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAc;;;;;;QACtC;QACA,IAAI,CAAC,OAAO,kBAAkB,EAAE;YAC9B,qBAAO,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAY;;;;;;QACpC;QACA,qBAAO,8OAAC,iIAAA,CAAA,QAAK;YAAC,SAAQ;sBAAU;;;;;;IAClC;IAEA,IAAI,iBAAiB,MAAM,KAAK,GAAG;QACjC,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;;sCACT,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;sCACX,8OAAC,gIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAEnB,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;kCACJ,cAAA,8OAAC,iIAAA,CAAA,mBAAgB;sCAAC;;;;;;;;;;;;;;;;;;;;;;IAO5B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC;8CAAK;;;;;;8CACN,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAe,MAAK;;sDACnC,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;kCAK3C,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAsB,iBAAiB,MAAM;;;;;;sDAC5D,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,iBAAiB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,MAAM,EAAE,QAAQ,uBAAuB,CAAC,GAAG;;;;;;sDAE3F,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,iBAAiB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,MAAM,EAAE,QAAQ,gBAAgB,CAAC,GAAG;;;;;;sDAEpF,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,iBAAiB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,MAAM,EAAE,WAAW,UAAU,CAAC,GAAG;;;;;;sDAEjF,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO9C,iBAAiB,GAAG,CAAC,CAAC;gBACrB,IAAI,CAAC,OAAO,MAAM,EAAE,OAAO;gBAE3B,MAAM,iBAAiB,OAAO,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,eAAe;gBACpF,MAAM,UAAU,cAAc,CAAC,OAAO,GAAG,CAAC;gBAC1C,MAAM,gBAAgB,UAAU,OAAO,MAAM,CAAC,gBAAgB,GAAG;gBAEjE,qBACE,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC;;gDAAK;gDAAM,OAAO,GAAG;;;;;;;sDACtB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,qBAAqB,OAAO,GAAG;;oDAE7C,wBAAU,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAAoB,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAC/D,UAAU,sBAAsB;;;;;;;;;;;;;;;;;;8CAIvC,8OAAC,gIAAA,CAAA,kBAAe;;wCAAC;wCACN,OAAO,MAAM,CAAC,QAAQ;wCAAC;wCACxB,OAAO,MAAM,CAAC,OAAO,CAAC,YAAY;wCAAC;wCACjC,OAAO,MAAM,CAAC,OAAO,CAAC,mBAAmB;;;;;;;;;;;;;sCAGvD,8OAAC,gIAAA,CAAA,cAAW;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;0DACJ,8OAAC,iIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;sEACP,8OAAC,iIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,iIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,iIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,iIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,iIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,iIAAA,CAAA,YAAS;sEAAC;;;;;;;;;;;;;;;;;0DAGf,8OAAC,iIAAA,CAAA,YAAS;0DACP,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC,iIAAA,CAAA,WAAQ;;0EACP,8OAAC,iIAAA,CAAA,YAAS;0EAAE,OAAO,YAAY;;;;;;0EAC/B,8OAAC,iIAAA,CAAA,YAAS;0EAAE,OAAO,SAAS;;;;;;0EAC5B,8OAAC,iIAAA,CAAA,YAAS;0EAAE,OAAO,MAAM;;;;;;0EACzB,8OAAC,iIAAA,CAAA,YAAS;0EAAE,OAAO,aAAa;;;;;;0EAChC,8OAAC,iIAAA,CAAA,YAAS;0EAAE,eAAe;;;;;;0EAC3B,8OAAC,iIAAA,CAAA,YAAS;0EACP,OAAO,eAAe,GAAG,OAAO,YAAY,GAAG;;;;;;;uDAPrC,GAAG,OAAO,GAAG,CAAC,CAAC,EAAE,OAAO,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;gCAgB5D,OAAO,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,mBACjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAgB;;;;;;wCAC7B,OAAO,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,OAAO,sBACpC,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;;oEAAwB;oEAC9B,MAAM,YAAY;oEAAC;oEAAI,MAAM,SAAS;;;;;;;0EAEhD,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,gBAAgB,MAAM,KAAK;0EAE1C,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAGpB,8OAAC;wDAAK,WAAU;kEAAqB,MAAM,KAAK;;;;;;;+CAbxC;;;;;;;;;;;;;;;;;;mBAzDT,OAAO,GAAG;;;;;YA8EzB;;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 1920, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Code/cover-genius-v1/ui-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { CSVUpload } from '@/components/csv-upload';\nimport { PNRProcessor } from '@/components/pnr-processor';\nimport { ResultsTable } from '@/components/results-table';\nimport { PNRRecord } from '@/types';\n\nexport default function Home() {\n  const [pnrs, setPnrs] = useState<string[]>([]);\n  const [results, setResults] = useState<PNRRecord[]>([]);\n\n  const handlePNRsExtracted = (extractedPnrs: string[]) => {\n    setPnrs(extractedPnrs);\n    setResults([]); // Clear previous results\n  };\n\n  const handleProcessingComplete = (processingResults: PNRRecord[]) => {\n    setResults(processingResults);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            Cover Genius Insurance Processor\n          </h1>\n          <p className=\"text-gray-600\">\n            Upload a CSV file with PNR numbers to process insurance data and generate SQL update queries\n          </p>\n        </div>\n\n        <div className=\"space-y-6\">\n          {/* Step 1: CSV Upload */}\n          <CSVUpload onPNRsExtracted={handlePNRsExtracted} />\n\n          {/* Step 2: PNR Processing */}\n          {pnrs.length > 0 && (\n            <PNRProcessor\n              pnrs={pnrs}\n              onProcessingComplete={handleProcessingComplete}\n            />\n          )}\n\n          {/* Step 3: Results Display */}\n          {results.length > 0 && (\n            <ResultsTable results={results} />\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAEtD,MAAM,sBAAsB,CAAC;QAC3B,QAAQ;QACR,WAAW,EAAE,GAAG,yBAAyB;IAC3C;IAEA,MAAM,2BAA2B,CAAC;QAChC,WAAW;IACb;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAK/B,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,mIAAA,CAAA,YAAS;4BAAC,iBAAiB;;;;;;wBAG3B,KAAK,MAAM,GAAG,mBACb,8OAAC,sIAAA,CAAA,eAAY;4BACX,MAAM;4BACN,sBAAsB;;;;;;wBAKzB,QAAQ,MAAM,GAAG,mBAChB,8OAAC,sIAAA,CAAA,eAAY;4BAAC,SAAS;;;;;;;;;;;;;;;;;;;;;;;AAMnC", "debugId": null}}]}