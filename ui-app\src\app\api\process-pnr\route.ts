import { NextRequest, NextResponse } from 'next/server';

// Import the main function from the parent directory
// We'll need to create a wrapper that can be imported
async function processMainFunction(pnr: string) {
  // For now, we'll simulate the main.js functionality
  // In a real implementation, you would:
  // 1. Import the main function from ../../../main.js
  // 2. Or create a shared module that both can use
  // 3. Or modify main.js to export its functions

  // Simulate processing delay
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Generate mock data based on the PNR
  const mockPolicyId = `${pnr}-POLICY-${Math.random().toString(36).substring(2, 11).toUpperCase()}`;
  const recordCount = Math.floor(Math.random() * 6) + 2; // 2-7 records
  const missingCount = Math.floor(Math.random() * recordCount);

  const passengers = [
    'JOHN DOE', 'JAN<PERSON> SMITH', 'MICHAEL JOHNSON', 'SARAH WILSON',
    'DAVID BROWN', 'LISA DAVIS', 'ROBERT MILLER', 'JENNIFER GARCIA'
  ];

  const flights = [
    'GYD to DOH (FZ710/009)', 'DOH to GYD (FZ004/707)',
    'DXB to DOH (FZ123/456)', 'DOH to DXB (FZ789/012)'
  ];

  const statuses = ['ACTIVE', 'BOARDED', 'CANCELED', 'NO_SHOW'];

  const insuranceRecords = [];
  const sqlQueries = [];

  for (let i = 1; i <= recordCount; i++) {
    const hasConfirmation = i > missingCount;
    const passenger = passengers[Math.floor(Math.random() * passengers.length)];
    const flight = flights[Math.floor(Math.random() * flights.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];

    const record = {
      recordNumber: i,
      passenger,
      flight,
      departureDate: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16).replace('T', ' '),
      insuranceId: `${mockPolicyId}/${Math.random().toString(36).substring(2, 11)}`,
      provider: Math.random() > 0.5 ? 'Cover Genius' : 'AIG',
      status,
      channel: 'MOBILE',
      purchaseDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16).replace('T', ' '),
      hasConfirmation,
      confirmation: hasConfirmation ? Math.floor(Math.random() * **********).toString() : undefined,
      withinPolicyPeriod: Math.random() > 0.1, // 90% within policy period
    };

    insuranceRecords.push(record);

    if (!hasConfirmation) {
      sqlQueries.push({
        recordNumber: i,
        passenger,
        query: `UPDATE P_FZ.RESERVATION_SEGS SET INSURANCE_CONF_NUM='${mockPolicyId}' WHERE CONFIRMATION_NUM='${pnr}' AND RECORD_NUM=${i};`
      });
    }
  }

  return {
    pnrNumber: pnr,
    policyId: mockPolicyId,
    insuranceRecords,
    policyStartDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10),
    policyEndDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10),
    summary: {
      totalRecords: recordCount,
      withConfirmation: recordCount - missingCount,
      missingConfirmation: missingCount,
      withinPolicyPeriod: insuranceRecords.filter(r => r.withinPolicyPeriod).length,
    },
    sqlQueries
  };
}

export async function POST(request: NextRequest) {
  try {
    const { pnr } = await request.json();

    if (!pnr || typeof pnr !== 'string') {
      return NextResponse.json(
        { error: 'PNR is required and must be a string' },
        { status: 400 }
      );
    }

    // Validate PNR format
    const cleanPnr = pnr.trim().toUpperCase();
    if (cleanPnr.length < 3 || cleanPnr.length > 10) {
      return NextResponse.json(
        { error: 'PNR must be between 3 and 10 characters' },
        { status: 400 }
      );
    }

    try {
      const result = await processMainFunction(cleanPnr);
      return NextResponse.json(result);
    } catch (processingError) {
      console.error('Error processing PNR:', processingError);

      return NextResponse.json(
        {
          error: 'Failed to process PNR',
          details: processingError instanceof Error ? processingError.message : 'Unknown processing error'
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
