import { NextRequest, NextResponse } from 'next/server';
import path from 'path';

// Import the main function from the copied main.js file
// We need to dynamically import the main.js file since it's CommonJS
async function processMainFunction(pnr: string) {
  try {
    // Use absolute path to the main.js file in the project root
    const mainPath = path.resolve(process.cwd(), 'main.js');

    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { main } = require(mainPath);

    // Call the main function with returnData=true to get structured data
    const result = await main(pnr, true);

    return result;
  } catch (error) {
    console.error('Error calling main function:', error);
    throw error;
  }
}

export async function POST(request: NextRequest) {
  try {
    const { pnr } = await request.json();

    if (!pnr || typeof pnr !== 'string') {
      return NextResponse.json(
        { error: 'PNR is required and must be a string' },
        { status: 400 }
      );
    }

    // Validate PNR format
    const cleanPnr = pnr.trim().toUpperCase();
    if (cleanPnr.length < 3 || cleanPnr.length > 10) {
      return NextResponse.json(
        { error: 'PNR must be between 3 and 10 characters' },
        { status: 400 }
      );
    }

    try {
      const result = await processMainFunction(cleanPnr);
      return NextResponse.json(result);
    } catch (processingError) {
      console.error('Error processing PNR:', processingError);

      return NextResponse.json(
        {
          error: 'Failed to process PNR',
          details: processingError instanceof Error ? processingError.message : 'Unknown processing error'
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
