/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/process-pnr/route";
exports.ids = ["app/api/process-pnr/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprocess-pnr%2Froute&page=%2Fapi%2Fprocess-pnr%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprocess-pnr%2Froute.ts&appDir=C%3A%5CUsers%5Cbasil.harrison%5CDocuments%5CCode%5Ccover-genius-v1%5Cui-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbasil.harrison%5CDocuments%5CCode%5Ccover-genius-v1%5Cui-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprocess-pnr%2Froute&page=%2Fapi%2Fprocess-pnr%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprocess-pnr%2Froute.ts&appDir=C%3A%5CUsers%5Cbasil.harrison%5CDocuments%5CCode%5Ccover-genius-v1%5Cui-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbasil.harrison%5CDocuments%5CCode%5Ccover-genius-v1%5Cui-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_basil_harrison_Documents_Code_cover_genius_v1_ui_app_src_app_api_process_pnr_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/process-pnr/route.ts */ \"(rsc)/./src/app/api/process-pnr/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/process-pnr/route\",\n        pathname: \"/api/process-pnr\",\n        filename: \"route\",\n        bundlePath: \"app/api/process-pnr/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\app\\\\api\\\\process-pnr\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_basil_harrison_Documents_Code_cover_genius_v1_ui_app_src_app_api_process_pnr_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZwcm9jZXNzLXBuciUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGcHJvY2Vzcy1wbnIlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZwcm9jZXNzLXBuciUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNiYXNpbC5oYXJyaXNvbiU1Q0RvY3VtZW50cyU1Q0NvZGUlNUNjb3Zlci1nZW5pdXMtdjElNUN1aS1hcHAlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q2Jhc2lsLmhhcnJpc29uJTVDRG9jdW1lbnRzJTVDQ29kZSU1Q2NvdmVyLWdlbml1cy12MSU1Q3VpLWFwcCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDeUQ7QUFDdEk7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFVzZXJzXFxcXGJhc2lsLmhhcnJpc29uXFxcXERvY3VtZW50c1xcXFxDb2RlXFxcXGNvdmVyLWdlbml1cy12MVxcXFx1aS1hcHBcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxccHJvY2Vzcy1wbnJcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3Byb2Nlc3MtcG5yL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvcHJvY2Vzcy1wbnJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3Byb2Nlc3MtcG5yL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcVXNlcnNcXFxcYmFzaWwuaGFycmlzb25cXFxcRG9jdW1lbnRzXFxcXENvZGVcXFxcY292ZXItZ2VuaXVzLXYxXFxcXHVpLWFwcFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxwcm9jZXNzLXBuclxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprocess-pnr%2Froute&page=%2Fapi%2Fprocess-pnr%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprocess-pnr%2Froute.ts&appDir=C%3A%5CUsers%5Cbasil.harrison%5CDocuments%5CCode%5Ccover-genius-v1%5Cui-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbasil.harrison%5CDocuments%5CCode%5Ccover-genius-v1%5Cui-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/process-pnr sync recursive":
/*!***************************************!*\
  !*** ./src/app/api/process-pnr/ sync ***!
  \***************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./src/app/api/process-pnr sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./src/app/api/process-pnr/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/process-pnr/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Import the main function from the copied main.js file\n// We need to dynamically import the main.js file since it's CommonJS\nasync function processMainFunction(pnr) {\n    try {\n        // Use absolute path to the main.js file in the project root\n        const mainPath = path__WEBPACK_IMPORTED_MODULE_1___default().resolve(process.cwd(), 'main.js');\n        // eslint-disable-next-line @typescript-eslint/no-require-imports\n        const { main } = __webpack_require__(\"(rsc)/./src/app/api/process-pnr sync recursive\")(mainPath);\n        // Call the main function with returnData=true to get structured data\n        const result = await main(pnr, true);\n        return result;\n    } catch (error) {\n        console.error('Error calling main function:', error);\n        throw error;\n    }\n}\nasync function POST(request) {\n    try {\n        const { pnr } = await request.json();\n        if (!pnr || typeof pnr !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'PNR is required and must be a string'\n            }, {\n                status: 400\n            });\n        }\n        // Validate PNR format\n        const cleanPnr = pnr.trim().toUpperCase();\n        if (cleanPnr.length < 3 || cleanPnr.length > 10) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'PNR must be between 3 and 10 characters'\n            }, {\n                status: 400\n            });\n        }\n        try {\n            const result = await processMainFunction(cleanPnr);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n        } catch (processingError) {\n            console.error('Error processing PNR:', processingError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to process PNR',\n                details: processingError instanceof Error ? processingError.message : 'Unknown processing error'\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/process-pnr/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprocess-pnr%2Froute&page=%2Fapi%2Fprocess-pnr%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprocess-pnr%2Froute.ts&appDir=C%3A%5CUsers%5Cbasil.harrison%5CDocuments%5CCode%5Ccover-genius-v1%5Cui-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbasil.harrison%5CDocuments%5CCode%5Ccover-genius-v1%5Cui-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();