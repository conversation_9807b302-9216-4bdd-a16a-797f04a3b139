<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cover Genius Insurance Processor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .sql-query {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 5px 0;
            white-space: pre-wrap;
        }
        .record {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background-color: white;
        }
        .missing {
            border-left: 4px solid #dc3545;
        }
        .has-confirmation {
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Cover Genius Insurance Processor</h1>
        
        <div class="form-group">
            <label for="pnr">Enter PNR Number:</label>
            <input type="text" id="pnr" placeholder="e.g., ALG5OR" value="ALG5OR">
        </div>
        
        <button onclick="processPNR()" id="processBtn">Process PNR</button>
        
        <div id="results"></div>
    </div>

    <script>
        async function processPNR() {
            const pnr = document.getElementById('pnr').value.trim();
            const resultsDiv = document.getElementById('results');
            const processBtn = document.getElementById('processBtn');
            
            if (!pnr) {
                resultsDiv.innerHTML = '<div class="error">Please enter a PNR number</div>';
                return;
            }
            
            processBtn.disabled = true;
            processBtn.textContent = 'Processing...';
            resultsDiv.innerHTML = '<div>Processing PNR: ' + pnr + '...</div>';
            
            try {
                const response = await fetch('/api/process-pnr', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ pnr: pnr })
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || 'Failed to process PNR');
                }
                
                displayResults(data);
                
            } catch (error) {
                resultsDiv.innerHTML = '<div class="error">Error: ' + error.message + '</div>';
            } finally {
                processBtn.disabled = false;
                processBtn.textContent = 'Process PNR';
            }
        }
        
        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            
            let html = '<div class="results">';
            html += '<h2>Processing Results</h2>';
            html += '<div class="success">Successfully processed PNR: ' + data.pnrNumber + '</div>';
            
            // Summary
            html += '<h3>Summary</h3>';
            html += '<p><strong>Policy ID:</strong> ' + data.policyId + '</p>';
            html += '<p><strong>Policy Period:</strong> ' + data.policyStartDate + ' to ' + data.policyEndDate + '</p>';
            html += '<p><strong>Total Records:</strong> ' + data.summary.totalRecords + '</p>';
            html += '<p><strong>With Confirmation:</strong> ' + data.summary.withConfirmation + '</p>';
            html += '<p><strong>Missing Confirmation:</strong> ' + data.summary.missingConfirmation + '</p>';
            html += '<p><strong>Within Policy Period:</strong> ' + data.summary.withinPolicyPeriod + '</p>';
            
            // Insurance Records
            html += '<h3>Insurance Records</h3>';
            data.insuranceRecords.forEach(record => {
                const cssClass = record.hasConfirmation ? 'has-confirmation' : 'missing';
                html += '<div class="record ' + cssClass + '">';
                html += '<h4>Record #' + record.recordNumber + ' - ' + record.passengerName + '</h4>';
                html += '<p><strong>Flight:</strong> ' + record.segmentInfo.flightNumber + ' (' + record.segmentInfo.origin + ' → ' + record.segmentInfo.destination + ')</p>';
                html += '<p><strong>Departure:</strong> ' + record.departureDate + '</p>';
                html += '<p><strong>Status:</strong> ' + record.statusText + '</p>';
                html += '<p><strong>Provider:</strong> ' + record.provider + '</p>';
                html += '<p><strong>Confirmation:</strong> ' + (record.hasConfirmation ? record.insuConfNum : 'MISSING') + '</p>';
                html += '</div>';
            });
            
            // SQL Queries
            if (data.sqlQueries.length > 0) {
                html += '<h3>SQL Update Queries (' + data.sqlQueries.length + ')</h3>';
                html += '<p>Copy and execute these queries to update missing insurance confirmations:</p>';
                data.sqlQueries.forEach(query => {
                    html += '<div class="sql-query">' + query.query + '</div>';
                });
            }
            
            html += '</div>';
            resultsDiv.innerHTML = html;
        }
        
        // Allow Enter key to submit
        document.getElementById('pnr').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                processPNR();
            }
        });
    </script>
</body>
</html>
