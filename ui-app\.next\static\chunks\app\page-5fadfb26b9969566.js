(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1634:(e,s,r)=>{Promise.resolve().then(r.bind(r,4257))},4257:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>B});var t=r(5155),a=r(2115),n=r(408),i=r.n(n),l=r(4624),c=r(2085),o=r(2596),d=r(9688);function u(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,d.QP)((0,o.$)(s))}let x=(0,c.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function m(e){let{className:s,variant:r,size:a,asChild:n=!1,...i}=e,c=n?l.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:u(x({variant:r,size:a,className:s})),...i})}function h(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card",className:u("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...r})}function p(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:u("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...r})}function g(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:u("leading-none font-semibold",s),...r})}function f(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:u("text-muted-foreground text-sm",s),...r})}function v(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:u("px-6",s),...r})}function j(e){let{className:s,type:r,...a}=e;return(0,t.jsx)("input",{type:r,"data-slot":"input",className:u("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...a})}let b=(0,c.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function N(e){let{className:s,variant:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"alert",role:"alert",className:u(b({variant:r}),s),...a})}function y(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"alert-description",className:u("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",s),...r})}let w=(0,c.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function C(e){let{className:s,variant:r,asChild:a=!1,...n}=e,i=a?l.DX:"span";return(0,t.jsx)(i,{"data-slot":"badge",className:u(w({variant:r}),s),...n})}var P=r(9869),k=r(7434),R=r(4416);function S(e){let{onPNRsExtracted:s}=e,[r,n]=(0,a.useState)(null),[l,c]=(0,a.useState)([]),[o,d]=(0,a.useState)([]),[u,x]=(0,a.useState)(""),[b,w]=(0,a.useState)(""),[S,O]=(0,a.useState)([]),_=(0,a.useCallback)(e=>{var s;let r=null==(s=e.target.files)?void 0:s[0];if(r){if(!r.name.toLowerCase().endsWith(".csv"))return void w("Please upload a CSV file");n(r),w(""),i().parse(r,{header:!0,skipEmptyLines:!0,complete:e=>{if(e.errors.length>0)return void w("CSV parsing error: ".concat(e.errors[0].message));let s=e.data;if(c(s),s.length>0){let e=Object.keys(s[0]);d(e);let r=e.find(e=>e.toLowerCase().includes("pnr")||e.toLowerCase().includes("confirmation")||e.toLowerCase().includes("booking"));r&&x(r)}},error:e=>{w("Failed to parse CSV: ".concat(e.message))}})}},[]),E=(0,a.useCallback)(()=>{if(!u||0===l.length)return void w("Please select a column containing PNR numbers");let e=l.map(e=>{var s;return null==(s=e[u])?void 0:s.trim().toUpperCase()}).filter(e=>e&&e.length>=3&&e.length<=10).filter((e,s,r)=>r.indexOf(e)===s);if(0===e.length)return void w("No valid PNR numbers found in the selected column");O(e),s(e),w("")},[u,l,s]),z=(0,a.useCallback)(()=>{n(null),c([]),d([]),x(""),O([]),w("")},[]);return(0,t.jsxs)(h,{className:"w-full",children:[(0,t.jsxs)(p,{children:[(0,t.jsxs)(g,{className:"flex items-center gap-2",children:[(0,t.jsx)(P.A,{className:"h-5 w-5"}),"Upload CSV File"]}),(0,t.jsx)(f,{children:"Upload a CSV file containing PNR numbers to process insurance data"})]}),(0,t.jsxs)(v,{className:"space-y-4",children:[r?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(k.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:r.name}),(0,t.jsxs)(C,{variant:"secondary",children:[l.length," rows"]})]}),(0,t.jsx)(m,{variant:"ghost",size:"sm",onClick:z,children:(0,t.jsx)(R.A,{className:"h-4 w-4"})})]}),o.length>0&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Select column containing PNR numbers:"}),(0,t.jsxs)("select",{value:u,onChange:e=>x(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"-- Select Column --"}),o.map(e=>(0,t.jsx)("option",{value:e,children:e},e))]})]}),u&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Preview of ",u," column (first 5 rows):"]}),(0,t.jsx)("div",{className:"bg-gray-50 p-3 rounded-lg",children:l.slice(0,5).map((e,s)=>(0,t.jsx)("div",{className:"text-sm",children:e[u]},s))})]}),(0,t.jsxs)(m,{onClick:E,disabled:!u,className:"w-full",children:["Extract PNR Numbers (",u?l.length:0," rows)"]})]}):(0,t.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[(0,t.jsx)(k.A,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Choose a CSV file or drag and drop it here"}),(0,t.jsx)(j,{type:"file",accept:".csv",onChange:_,className:"max-w-xs mx-auto"})]})]}),b&&(0,t.jsx)(N,{variant:"destructive",children:(0,t.jsx)(y,{children:b})}),S.length>0&&(0,t.jsx)(N,{children:(0,t.jsxs)(y,{children:["Successfully extracted ",S.length," unique PNR numbers: ",S.slice(0,5).join(", "),S.length>5&&" and ".concat(S.length-5," more...")]})})]})]})}var O=r(1838);function _(e){let{className:s,value:r,...a}=e;return(0,t.jsx)(O.bL,{"data-slot":"progress",className:u("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",s),...a,children:(0,t.jsx)(O.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})}var E=r(5690),z=r(133);function A(e){let{pnrs:s,onProcessingComplete:r}=e,[n,i]=(0,a.useState)({pnrs:s.map(e=>({pnr:e,status:"pending",progress:0})),isProcessing:!1,completedCount:0,totalCount:s.length}),l=(0,a.useCallback)(async e=>{try{i(s=>({...s,pnrs:s.pnrs.map(s=>s.pnr===e?{...s,status:"processing",progress:10}:s)}));for(let s=20;s<=90;s+=20)await new Promise(e=>setTimeout(e,500)),i(r=>({...r,pnrs:r.pnrs.map(r=>r.pnr===e?{...r,progress:s}:r)}));let s=await fetch("/api/process-pnr",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({pnr:e})});if(!s.ok)throw Error("HTTP error! status: ".concat(s.status));let r=await s.json();i(s=>({...s,pnrs:s.pnrs.map(s=>s.pnr===e?{...s,status:"completed",progress:100,result:r}:s),completedCount:s.completedCount+1}))}catch(s){i(r=>({...r,pnrs:r.pnrs.map(r=>r.pnr===e?{...r,status:"error",progress:0,error:s instanceof Error?s.message:"Unknown error"}:r),completedCount:r.completedCount+1}))}},[]),c=(0,a.useCallback)(async()=>{for(let e of(i(e=>({...e,isProcessing:!0,completedCount:0})),n.pnrs))"pending"===e.status&&await l(e.pnr);i(e=>({...e,isProcessing:!1})),r(n.pnrs)},[n.pnrs,l,r]),o=(0,a.useCallback)(()=>{i({pnrs:s.map(e=>({pnr:e,status:"pending",progress:0})),isProcessing:!1,completedCount:0,totalCount:s.length})},[s]),d=e=>{switch(e){case"pending":default:return"secondary";case"processing":case"completed":return"default";case"error":return"destructive"}},u=n.totalCount>0?n.completedCount/n.totalCount*100:0;return(0,t.jsxs)(h,{className:"w-full",children:[(0,t.jsxs)(p,{children:[(0,t.jsxs)(g,{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"Process PNR Records"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(m,{onClick:c,disabled:n.isProcessing,size:"sm",children:[(0,t.jsx)(E.A,{className:"h-4 w-4 mr-2"}),n.isProcessing?"Processing...":"Start Processing"]}),(0,t.jsxs)(m,{onClick:o,disabled:n.isProcessing,variant:"outline",size:"sm",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"Reset"]})]})]}),(0,t.jsxs)(f,{children:["Process ",n.totalCount," PNR records to extract insurance data"]})]}),(0,t.jsxs)(v,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{children:"Overall Progress"}),(0,t.jsxs)("span",{children:[n.completedCount," / ",n.totalCount]})]}),(0,t.jsx)(_,{value:u,className:"w-full"})]}),(0,t.jsx)("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:n.pnrs.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("span",{className:"font-mono font-medium",children:e.pnr}),(0,t.jsx)(C,{variant:d(e.status),children:e.status})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:["processing"===e.status&&(0,t.jsx)("div",{className:"w-24",children:(0,t.jsx)(_,{value:e.progress,className:"h-2"})}),"completed"===e.status&&e.result&&(0,t.jsxs)("span",{className:"text-sm text-green-600",children:[e.result.summary.missingConfirmation," missing"]}),"error"===e.status&&(0,t.jsx)("span",{className:"text-sm text-red-600 max-w-xs truncate",children:e.error})]})]},e.pnr))}),n.completedCount>0&&(0,t.jsx)(N,{children:(0,t.jsxs)(y,{children:["Completed: ",n.pnrs.filter(e=>"completed"===e.status).length,", Errors: ",n.pnrs.filter(e=>"error"===e.status).length,", Remaining: ",n.pnrs.filter(e=>"pending"===e.status).length]})})]})]})}function L(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:u("w-full caption-bottom text-sm",s),...r})})}function q(e){let{className:s,...r}=e;return(0,t.jsx)("thead",{"data-slot":"table-header",className:u("[&_tr]:border-b",s),...r})}function Q(e){let{className:s,...r}=e;return(0,t.jsx)("tbody",{"data-slot":"table-body",className:u("[&_tr:last-child]:border-0",s),...r})}function T(e){let{className:s,...r}=e;return(0,t.jsx)("tr",{"data-slot":"table-row",className:u("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",s),...r})}function U(e){let{className:s,...r}=e;return(0,t.jsx)("th",{"data-slot":"table-head",className:u("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...r})}function V(e){let{className:s,...r}=e;return(0,t.jsx)("td",{"data-slot":"table-cell",className:u("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...r})}var D=r(1788),F=r(8749),I=r(2657),M=r(4357);function X(e){let{results:s}=e,[r,n]=(0,a.useState)({}),i=s.filter(e=>"completed"===e.status&&e.result),l=async e=>{try{await navigator.clipboard.writeText(e)}catch(e){console.error("Failed to copy text: ",e)}},c=e=>{n(s=>({...s,[e]:!s[e]}))},o=e=>e.hasConfirmation?e.withinPolicyPeriod?(0,t.jsx)(C,{variant:"default",children:"OK"}):(0,t.jsx)(C,{variant:"secondary",children:"Outside Policy Period"}):(0,t.jsx)(C,{variant:"destructive",children:"Missing Confirmation"});return 0===i.length?(0,t.jsxs)(h,{className:"w-full",children:[(0,t.jsxs)(p,{children:[(0,t.jsx)(g,{children:"Processing Results"}),(0,t.jsx)(f,{children:"Results will appear here as PNRs are processed"})]}),(0,t.jsx)(v,{children:(0,t.jsx)(N,{children:(0,t.jsx)(y,{children:"No completed results yet. Start processing PNRs to see the insurance data."})})})]}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(h,{children:[(0,t.jsx)(p,{children:(0,t.jsxs)(g,{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"Processing Results Summary"}),(0,t.jsxs)(m,{onClick:()=>{let e=[],s=[];i.forEach(r=>{r.result&&(r.result.insuranceRecords.forEach(s=>{e.push({PNR:r.pnr,PolicyID:r.result.policyId,...s})}),r.result.sqlQueries.forEach(e=>{s.push("-- PNR: ".concat(r.pnr,", Record: ").concat(e.recordNumber,", Passenger: ").concat(e.passenger)),s.push(e.query),s.push("")}))});let r=[Object.keys(e[0]||{}).join(","),...e.map(e=>Object.values(e).map(e=>"string"==typeof e&&e.includes(",")?'"'.concat(e,'"'):e).join(","))].join("\n"),t=s.join("\n"),a=new Blob([r],{type:"text/csv"}),n=URL.createObjectURL(a),l=document.createElement("a");l.href=n,l.download="insurance-records-".concat(new Date().toISOString().split("T")[0],".csv"),l.click();let c=new Blob([t],{type:"text/sql"}),o=URL.createObjectURL(c),d=document.createElement("a");d.href=o,d.download="update-queries-".concat(new Date().toISOString().split("T")[0],".sql"),d.click()},size:"sm",children:[(0,t.jsx)(D.A,{className:"h-4 w-4 mr-2"}),"Export All"]})]})}),(0,t.jsx)(v,{children:(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:i.length}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Processed PNRs"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:i.reduce((e,s)=>{var r;return e+((null==(r=s.result)?void 0:r.summary.missingConfirmation)||0)},0)}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Missing Confirmations"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:i.reduce((e,s)=>{var r;return e+((null==(r=s.result)?void 0:r.summary.totalRecords)||0)},0)}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Total Records"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:i.reduce((e,s)=>{var r;return e+((null==(r=s.result)?void 0:r.sqlQueries.length)||0)},0)}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"SQL Queries"})]})]})})]}),i.map(e=>{if(!e.result)return null;let s=e.result.insuranceRecords.filter(e=>!e.hasConfirmation),a=r[e.pnr],n=a?e.result.insuranceRecords:s;return(0,t.jsxs)(h,{children:[(0,t.jsxs)(p,{children:[(0,t.jsxs)(g,{className:"flex items-center justify-between",children:[(0,t.jsxs)("span",{children:["PNR: ",e.pnr]}),(0,t.jsx)("div",{className:"flex gap-2",children:(0,t.jsxs)(m,{variant:"outline",size:"sm",onClick:()=>c(e.pnr),children:[a?(0,t.jsx)(F.A,{className:"h-4 w-4 mr-2"}):(0,t.jsx)(I.A,{className:"h-4 w-4 mr-2"}),a?"Show Missing Only":"Show All Records"]})})]}),(0,t.jsxs)(f,{children:["Policy: ",e.result.policyId," | Total: ",e.result.summary.totalRecords," | Missing: ",e.result.summary.missingConfirmation]})]}),(0,t.jsxs)(v,{children:[(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)(L,{children:[(0,t.jsx)(q,{children:(0,t.jsxs)(T,{children:[(0,t.jsx)(U,{children:"Record #"}),(0,t.jsx)(U,{children:"Passenger"}),(0,t.jsx)(U,{children:"Flight"}),(0,t.jsx)(U,{children:"Departure"}),(0,t.jsx)(U,{children:"Status"}),(0,t.jsx)(U,{children:"Confirmation"})]})}),(0,t.jsx)(Q,{children:n.map(s=>(0,t.jsxs)(T,{children:[(0,t.jsx)(V,{children:s.recordNumber}),(0,t.jsx)(V,{children:s.passenger}),(0,t.jsx)(V,{children:s.flight}),(0,t.jsx)(V,{children:s.departureDate}),(0,t.jsx)(V,{children:o(s)}),(0,t.jsx)(V,{children:s.hasConfirmation?s.confirmation:"Missing"})]},"".concat(e.pnr,"-").concat(s.recordNumber)))})]})}),e.result.sqlQueries.length>0&&(0,t.jsxs)("div",{className:"mt-6 space-y-3",children:[(0,t.jsx)("h4",{className:"font-semibold",children:"SQL Update Queries"}),e.result.sqlQueries.map((e,s)=>(0,t.jsxs)("div",{className:"bg-gray-900 text-gray-100 p-3 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsxs)("span",{className:"text-sm text-gray-400",children:["Record ",e.recordNumber," - ",e.passenger]}),(0,t.jsx)(m,{variant:"ghost",size:"sm",onClick:()=>l(e.query),children:(0,t.jsx)(M.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("code",{className:"text-sm font-mono",children:e.query})]},s))]})]})]},e.pnr)})]})}function B(){let[e,s]=(0,a.useState)([]),[r,n]=(0,a.useState)([]);return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Cover Genius Insurance Processor"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Upload a CSV file with PNR numbers to process insurance data and generate SQL update queries"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(S,{onPNRsExtracted:e=>{s(e),n([])}}),e.length>0&&(0,t.jsx)(A,{pnrs:e,onProcessingComplete:e=>{n(e)}}),r.length>0&&(0,t.jsx)(X,{results:r})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[558,441,684,358],()=>s(1634)),_N_E=e.O()}]);