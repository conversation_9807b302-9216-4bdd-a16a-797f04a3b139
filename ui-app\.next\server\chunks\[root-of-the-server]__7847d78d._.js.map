{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Code/cover-genius-v1/ui-app/src/app/api/process-pnr/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport path from 'path';\n\n// Import the main function from the copied main.js file\n// We need to dynamically import the main.js file since it's CommonJS\nasync function processMainFunction(pnr: string) {\n  try {\n    // Path to the main.js file in the project root\n    const mainPath = path.resolve(process.cwd(), 'main.js');\n\n    // Dynamically import the main function using dynamic import\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    const { main } = require(mainPath);\n\n    // Call the main function with returnData=true to get structured data\n    const result = await main(pnr, true);\n\n    return result;\n  } catch (error) {\n    console.error('Error calling main function:', error);\n    throw error;\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { pnr } = await request.json();\n\n    if (!pnr || typeof pnr !== 'string') {\n      return NextResponse.json(\n        { error: 'PNR is required and must be a string' },\n        { status: 400 }\n      );\n    }\n\n    // Validate PNR format\n    const cleanPnr = pnr.trim().toUpperCase();\n    if (cleanPnr.length < 3 || cleanPnr.length > 10) {\n      return NextResponse.json(\n        { error: 'PNR must be between 3 and 10 characters' },\n        { status: 400 }\n      );\n    }\n\n    try {\n      const result = await processMainFunction(cleanPnr);\n      return NextResponse.json(result);\n    } catch (processingError) {\n      console.error('Error processing PNR:', processingError);\n\n      return NextResponse.json(\n        {\n          error: 'Failed to process PNR',\n          details: processingError instanceof Error ? processingError.message : 'Unknown processing error'\n        },\n        { status: 500 }\n      );\n    }\n\n  } catch (error) {\n    console.error('API Error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,wDAAwD;AACxD,qEAAqE;AACrE,eAAe,oBAAoB,GAAW;IAC5C,IAAI;QACF,+CAA+C;QAC/C,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI;QAE7C,4DAA4D;QAC5D,iEAAiE;QACjE,MAAM,EAAE,IAAI,EAAE;;;;;QAEd,qEAAqE;QACrE,MAAM,SAAS,MAAM,KAAK,KAAK;QAE/B,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,QAAQ,IAAI;QAElC,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuC,GAChD;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,WAAW,IAAI,IAAI,GAAG,WAAW;QACvC,IAAI,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,GAAG,IAAI;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0C,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,oBAAoB;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B,EAAE,OAAO,iBAAiB;YACxB,QAAQ,KAAK,CAAC,yBAAyB;YAEvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,SAAS,2BAA2B,QAAQ,gBAAgB,OAAO,GAAG;YACxE,GACA;gBAAE,QAAQ;YAAI;QAElB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}